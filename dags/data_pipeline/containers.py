# coding=utf-8
import asyncio
import atexit
import base64
import json
import logging.config
import os
import signal
import time
from contextlib import asynccontextmanager, contextmanager
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from logging import Logger
from types import MethodType
from typing import (
    Dict, Any, Optional,
    Self, AsyncGenerator, Generator
)
from zoneinfo import ZoneInfo

import aiohttp
import asyncpg
import pandas as pd
from dependency_injector import containers, providers
from dependency_injector.wiring import inject, Provide
from pykeepass import PyKeePass

from sqlalchemy import create_engine, literal, case, cast, func, TEXT, select, inspect
from sqlalchemy.engine import URL
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, AsyncEngine
from sqlalchemy.exc import InterfaceError, DisconnectionError
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy_utils import LtreeType
import weakref

try:
    from dbmodels.issue import Issue
    from dbmodels.user import User
except ModuleNotFoundError:
    from .dbmodels.issue import Issue
    from .dbmodels.user import User

# logger = logging.getLogger(__name__)

@dataclass
class EntryDetails:
    username: str
    password: str
    url: str | None = None
    custom_properties: Dict[str, Any] = field(default_factory=dict)

    def __repr__(self) -> str:
        return (f"EntryDetails(username={self.username!r}, password='****', "
                f"url={self.url!r}, custom_properties={self.custom_properties!r})")

    def __str__(self) -> str:
        return (f"EntryDetails:\n"
                f"  Username: {self.username}\n"
                f"  Password: ****\n"
                f"  URL: {self.url}\n"
                f"  Custom Properties: {self.custom_properties}")


class EntryDetailsBuilder:
    def __init__(self):
        self._entry_details = EntryDetails(username='', password='')

    def set_username(self, username: str) -> 'EntryDetailsBuilder':
        self._entry_details.username = username
        return self

    def set_password(self, password: str) -> 'EntryDetailsBuilder':
        self._entry_details.password = password
        return self

    def set_url(self, url: Optional[str]) -> 'EntryDetailsBuilder':
        self._entry_details.url = url
        return self

    def add_custom_property(self, key: str, value: Any) -> 'EntryDetailsBuilder':
        self._entry_details.custom_properties[key] = value
        return self

    def build(self) -> EntryDetails:
        return self._entry_details


class CircuitState(Enum):
    CLOSED = "closed"  # Normal operation
    OPEN = "open"  # Circuit breaker tripped, blocking requests
    HALF_OPEN = "half_open"  # Testing if service recovered


@dataclass
class CircuitBreakerConfig:
    failure_threshold: int = 3
    recovery_timeout: float = 30.0  # seconds
    half_open_max_calls: int = 3



class LoggerContainer(containers.DeclarativeContainer):
    """
    Centralized logging container that provides configured loggers for the application.

    This container manages:
    - Logger configuration from YAML files
    - Multiple logger instances (main, profiled, debugging)
    - Proper resource management for logging setup
    - Consistent logger naming and configuration

    The container uses the Flowers refactoring pattern by:
    - Centralizing logger configuration
    - Providing reusable logger instances
    - Managing logging resources lifecycle
    - Enabling dependency injection of loggers

    Attributes:
        config_file: Configuration provider for logging settings
        log_resource: Resource provider for logging configuration
        logger: Main application logger (environment-specific)
        profiled_logger: Logger for performance profiling
        debug_logger: Logger for debugging operations

    Example:
        >>> logger_container = LoggerContainer()
        >>> logger_container.init_resources()
        >>> logger = logger_container.logger()
        >>> logger.info("Application started")
    """

    # Centralized configuration path management
    config_path = os.getenv("LOGGING_CONFIG_PATH", "logging_config.yaml")
    config_file = providers.Configuration(yaml_files=[config_path])

    # Wiring configuration for dependency injection
    wiring_config = containers.WiringConfiguration(
        modules=[
            "__main__", __name__,
            "dags.data_pipeline.utilities.profilers",
        ]
    )

    # Resource provider for logging configuration - properly managed
    log_resource = providers.Resource(
        logging.config.dictConfig,
        config=config_file.logging,
    )

    # Logger providers with proper dependency on log_resource
    logger = providers.Singleton(
        logging.getLogger,
        name=config_file.Environment.Env
    )

    profiled_logger = providers.Singleton(
        logging.getLogger,
        name="profiled_logger"
    )

    # Additional specialized loggers
    debug_logger = providers.Singleton(
        logging.getLogger,
        name="debug_utils"
    )

    debugging_monitor_logger = providers.Singleton(
        logging.getLogger,
        name="debugging_monitor"
    )


class ConnectionRecoveryMixin:
    """
    Mixin to add connection recovery capabilities to PostgresSessionManager.

    This mixin implements the Flowers refactoring pattern by:
    - Separating connection recovery concerns from main session management
    - Providing reusable recovery logic across different session managers
    - Using dependency injection for logger access
    - Centralizing retry and recovery configuration

    Attributes:
        MAX_RETRIES: Maximum number of retry attempts for connection recovery
        RETRY_DELAY: Base delay between retry attempts (exponential backoff)
        logger: Injected logger instance for recovery operations
    """

    MAX_RETRIES = 3
    RETRY_DELAY = 1.0
    engine_async: Optional[AsyncEngine]
    _create_engine_async: MethodType

    @inject
    def __init__(self, logger: Logger = Provide[LoggerContainer.logger], *args, **kwargs):
        """
        Initialize the connection recovery mixin.

        Args:
            logger: Injected logger instance from LoggerContainer
            *args: Additional positional arguments for parent classes
            **kwargs: Additional keyword arguments for parent classes
        """
        self.logger = logger
        super().__init__(*args, **kwargs)

    async def _handle_connection_error(self, error: Exception, operation_name: str = "operation") -> bool:
        """
        Handle connection errors and determine if retry is appropriate.

        Args:
            error: The exception that occurred
            operation_name: Name of the operation for logging

        Returns:
            bool: True if the operation should be retried, False otherwise
        """
        # Check if this is a connection-related error
        if isinstance(error, (
                InterfaceError,
                DisconnectionError,
                asyncpg.exceptions.InterfaceError,
                asyncpg.exceptions.ConnectionDoesNotExistError,
                asyncpg.exceptions.InvalidTransactionStateError,
                ConnectionError,
        )):
            self.logger.warning(f"Connection error during {operation_name}: {error}")
            return True

        # Check for specific SQLAlchemy error messages
        error_msg = str(error).lower()
        if any(msg in error_msg for msg in [
            'connection is closed',
            'connection was closed',
            'connection terminated',
            'server closed the connection',
            'connection lost',
            'connection broken'
        ]):
            self.logger.warning(f"Connection-related error during {operation_name}: {error}")
            return True

        return False

    async def _recreate_async_engine(self):
        """Recreate the async engine to establish new connections."""
        self.logger.info("Recreating async database engine...")

        if self.engine_async:
            try:
                await self.engine_async.dispose()
            except Exception as err:
                self.logger.warning(f"Error disposing old engine: {err}")

        # Recreate the engine
        self._create_engine_async()
        self.logger.info("Async database engine recreated successfully")

class PostgresSessionManager:
    """
    Manages PostgreSQL database connections with both synchronous and asynchronous support.

    This class provides a comprehensive database session manager that handles:
    - Both sync (psycopg2) and async (asyncpg) database connections
    - Connection pooling and lifecycle management
    - Schema switching capabilities
    - Automatic cleanup and resource disposal
    - Context managers for safe session handling

    Attributes:
        schema (str): Current database schema
        entry (EntryDetails): Database connection details
        rw (bool): Whether this is a read-write connection
        engine: Synchronous SQLAlchemy engine
        engine_async: Asynchronous SQLAlchemy engine

    Example:
        >>> entry = EntryDetails(username="user", password="pass", ...)
        >>> manager = PostgresSessionManager(entry, "public", rw=True)
        >>>
        >>> # Synchronous usage
        >>> with manager.session() as pg_session:
        ...     result = pg_session.execute("SELECT 1")
        >>>
        >>> # Asynchronous usage
        # >>> async with manager.async_session() as session:
        # ...     result = await session.execute("SELECT 1")
        >>>
        >>> # Schema switching
        >>> manager.update_schema("plat")
        >>>
        >>> # Cleanup
        >>> manager.close()  # or await manager.aclose()
    """
    # Class-level registry to track all instances for cleanup
    _instances = weakref.WeakSet()
    logger: Logger = None

    @inject
    def __init__(
            self, entry: EntryDetails, schema: str, rw: bool = True,
            logger: Logger = Provide[LoggerContainer.logger]
    ):
        """
        Initialize PostgresSessionManager with dependency injection.

        This implementation follows the Flowers refactoring pattern by:
        - Using dependency injection for logger access
        - Centralizing configuration management
        - Providing clean separation of concerns
        - Enabling testability through injectable dependencies

        Args:
            entry: Database connection details from KeePass
            schema: Database schema to use for operations
            rw: Whether this is a read-write connection (default: True)
            logger: Injected logger instance from LoggerContainer
        """
        self.schema = schema
        self.entry = entry
        self.rw = rw
        self.engine_async = None  # Initialize with None
        self.engine = None  # Initialize with None
        self.closed = False
        type(self).logger = logger  # Use instance-level logger instead of class-level

        # Register this instance for cleanup
        PostgresSessionManager._instances.add(self)

        self._create_engine_async()
        self._create_engine()

    def _create_engine_async(self):
        """Create asynchronous PostgreSQL engine with asyncpg driver."""
        self.DATABASE_URL_ASYNC = URL.create(
            drivername="postgresql+asyncpg",
            username=self.entry.username,
            password=self.entry.password,
            host=str(self.entry.custom_properties["DB_SERVER_NAME"]),
            port=self.entry.custom_properties["DB_SERVER_RW_PORT"] if self.rw else self.entry.custom_properties[
                "DB_SERVER_RO_PORT"],
            database=str(self.entry.custom_properties["DB_NAME"])
        )
        self.engine_async = create_async_engine(
            self.DATABASE_URL_ASYNC,
            echo=False,
            pool_size=20,  # Adjust pool size as needed
            max_overflow=30,  # Adjust max overflow as needed
            pool_timeout=30,  # Timeout in seconds for acquiring a connection from the pool
            pool_recycle=3600,  # Add connection recycling
            pool_pre_ping=True,  # Add connection health checks
            echo_pool=False,
            isolation_level="READ COMMITTED",
            # Add connection arguments for better handling
            connect_args={
                "server_settings": {
                    "application_name": f"airflow_app_{self.schema}",
                }
            }
        ).execution_options(
            schema_translate_map={None: self.schema}
        )

    def _create_engine(self):
        """Create synchronous PostgreSQL engine with psycopg2 driver."""
        # Ensure we're using psycopg2, not psycopg3
        self.DATABASE_URL = URL.create(
            drivername="postgresql+psycopg2",
            username=self.entry.username,
            password=self.entry.password,
            host=str(self.entry.custom_properties["DB_SERVER_NAME"]),
            port=self.entry.custom_properties["DB_SERVER_RW_PORT"] if self.rw else self.entry.custom_properties[
                "DB_SERVER_RO_PORT"],
            database=str(self.entry.custom_properties["DB_NAME"])
        )

        self.engine = create_engine(
            self.DATABASE_URL,
            pool_size=20,
            max_overflow=10,
            pool_recycle=3600,
            pool_timeout=30,
            pool_pre_ping=True,
            echo=False,
            echo_pool=False,
            isolation_level="READ COMMITTED"
        ).execution_options(
            schema_translate_map={None: self.schema}
        )

    # ADD: Proper cleanup methods
    def close(self):
        """Synchronous cleanup method."""
        if self.closed:
            return
        self.closed = True
        try:
            """Explicitly close all connections and dispose engines."""
            if self.engine:
                self.engine.dispose()
                self.engine = None
            # Handle async engine disposal properly
            if self.engine_async:
                # Use run_until_complete for async engine disposal

                try:
                    loop = asyncio.get_event_loop()
                    # Schedule the disposal as a task
                    loop.create_task(self._async_dispose())

                    # if loop.is_running():
                    #     # If we're in an async context, schedule disposal
                    #     asyncio.create_task(self.engine_async.dispose())
                    # else:
                    #     loop.run_until_complete(self.engine_async.dispose())
                except RuntimeError:
                    # No running loop, create a new one for cleanup
                    try:
                        asyncio.run(self._async_dispose())
                    except Exception as err:
                        self.logger.warning(f"Error during async engine disposal: {err}")
                    # Create new event loop if none exists
                    # new_loop = asyncio.new_event_loop()
                    # new_loop.run_until_complete(self.engine_async.dispose())
                    # new_loop.close()
        except Exception as err:
            self.logger.warning(f"Error during PostgresSessionManager cleanup: {err}")

    async def _async_dispose(self):
        """Helper method for async engine disposal."""
        if self.engine_async:
            try:
                await self.engine_async.dispose()
                self.engine_async = None
            except asyncio.CancelledError:
                # Handle cancellation gracefully
                self.logger.info("Async engine disposal was cancelled")
                raise
            except Exception as err:
                self.logger.warning(f"Error disposing async engine: {err}")
                raise

    async def aclose(self):
        """Async cleanup method."""
        if self.closed:
            return

        self.closed = True
        try:
            if self.engine:
                self.engine.dispose()
                self.engine = None
            if self.engine_async:
                await self.engine_async.dispose()
                self.engine_async = None
        except asyncio.CancelledError:
            self.logger.info("PostgresSessionManager async cleanup was cancelled")
            raise
        except Exception as err:
            self.logger.warning(f"Error during PostgresSessionManager async cleanup: {err}")


    def __del__(self):
        """Ensure cleanup on garbage collection."""
        if not self.closed:
            try:
                # Only dispose sync engine in __del__ to avoid event loop issues
                if self.engine:
                    self.engine.dispose()
                # self.engine = None
            except Exception:
                pass

    @contextmanager
    def session(self) -> Generator[Session, None, None]:
        """Context manager for sync sessions."""
        if self.closed:
            raise RuntimeError("PostgresSessionManager has been closed")

        session_maker = sessionmaker(
            bind=self.engine,
            autocommit=False,
            autoflush=False,
        )
        session = session_maker()
        try:
            yield session
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()

    @asynccontextmanager
    async def async_session(self) -> AsyncGenerator[Any, Any]:
        if self.closed:
            raise RuntimeError("PostgresSessionManager has been closed")
        async_session = sessionmaker(
            bind=self.engine_async,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=False,
            autocommit=False,
        )
        async with async_session() as session:
            try:
                yield session
            except asyncio.CancelledError:
                await session.rollback()
                self.logger.info("Async session was cancelled, rolled back")
                raise
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()

    def update_schema(self, new_schema: str) -> Self:
        """Dynamically updates the schema and refreshes the engines."""
        if self.closed:
            raise RuntimeError("PostgresSessionManager has been closed")

        self.schema = new_schema

        if self.engine_async:
            # Update schema_translate_map on existing engines
            self.engine_async = self.engine_async.execution_options(
                schema_translate_map={None: self.schema}
            )
        if self.engine:
            self.engine = self.engine.execution_options(
                schema_translate_map={None: self.schema}
            )

        return self

    def get_schemas(self) -> list[str]:
        """
        Retrieve all schemas from the regular (sync) engine.

        :return: List of schema names.
        """
        if self.closed:
            raise RuntimeError("PostgresSessionManager has been closed")
        inspector = inspect(self.engine, raiseerr=True)
        return inspector.get_schema_names()

    async def get_schemas_async(self) -> list[str]:
        """Retrieve all schemas from the async engine."""
        if self.closed:
            raise RuntimeError("PostgresSessionManager has been closed")

        async with self.engine_async.connect() as connection:
            return await connection.run_sync(
                lambda conn: inspect(conn).get_schema_names()
            )

    @classmethod
    async def cleanup_all_instances(cls):
        """Cleanup all active instances. Call this during application shutdown."""
        cleanup_tasks = []
        for instance in list(cls._instances):
            if not instance.closed:
                cleanup_tasks.append(instance.aclose())

        if cleanup_tasks:
            try:
                await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            except Exception as err:
                cls.logger.warning(f"Error during cleanup of all instances: {err}")


class DatabaseLifecycleManager:
    """Manages the lifecycle of database connections for the application."""

    @inject
    def __init__(self, logger: Logger = Provide[LoggerContainer.logger]):
        self._session_managers = []
        self._cleanup_registered = False
        self._setup_cleanup_handlers()
        self.logger = logger

    def register_session_manager(self, session_manager: PostgresSessionManager):
        """Register a session manager for cleanup."""
        self._session_managers.append(session_manager)

    def _setup_cleanup_handlers(self):
        """Setup cleanup handlers for different shutdown scenarios."""
        if self._cleanup_registered:
            return

        # Register atexit handler for normal program termination
        atexit.register(self._sync_cleanup)

        # Register signal handlers for graceful shutdown
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)

        self._cleanup_registered = True

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        self.logger.info(f"Received signal {signum}, cleaning up database connections...{frame}")
        self._sync_cleanup()

    def _sync_cleanup(self):
        """Synchronous cleanup method."""
        for session_manager in self._session_managers:
            try:
                session_manager.close()
            except Exception as err:
                self.logger.warning(f"Error cleaning up session manager: {err}")

        self._session_managers.clear()

    async def async_cleanup(self):
        """Asynchronous cleanup method."""
        cleanup_tasks = []
        for session_manager in self._session_managers:
            if not session_manager.closed:
                cleanup_tasks.append(session_manager.aclose())

        if cleanup_tasks:
            try:
                await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            except Exception as err:
                self.logger.warning(f"Error during async cleanup: {err}")

        self._session_managers.clear()


# Global lifecycle manager
db_lifecycle_manager = DatabaseLifecycleManager()


class ManagedPostgresSessionManager(PostgresSessionManager):
    """
    PostgresSessionManager that auto-registers with the global lifecycle manager.

    This class extends PostgresSessionManager to automatically register itself
    with the global database lifecycle manager for automatic cleanup during
    application shutdown. This ensures that database connections are properly
    closed even if explicit cleanup is not called.

    The lifecycle manager handles cleanup through:
    - atexit handlers for normal program termination
    - Signal handlers for SIGTERM and SIGINT
    - Manual cleanup methods for controlled shutdown

    Args:
        entry (EntryDetails): Database connection details
        schema (str): Database schema to use
        rw (bool): Whether this is a read-write connection (default: True)

    Example:
        >>> entry = EntryDetails(username="user", password="pass", ...)
        >>> manager = ManagedPostgresSessionManager(entry, "plat", rw=True)
        >>> # Manager is automatically registered for cleanup
        >>> with manager.session() as session:
        ...     # Database operations
        ...     pass
        >>> # Cleanup will happen automatically on application shutdown
    """

    def __init__(self, entry: EntryDetails, schema: str, rw: bool = True, **kwargs):
        super().__init__(entry=entry, schema=schema, rw=rw, **kwargs)
        # Auto-register with lifecycle manager
        db_lifecycle_manager.register_session_manager(self)

# Updated container with managed session managers
class EnhancedPostgresSessionManager(PostgresSessionManager, ConnectionRecoveryMixin):
    """
    Enhanced PostgresSessionManager with connection recovery capabilities.

    This class extends the base PostgresSessionManager to handle connection
    failures gracefully by implementing automatic retry logic and connection
    recovery mechanisms.
    """

    @asynccontextmanager
    async def async_session_with_retry(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Context manager for async sessions with automatic retry on connection failures.

        This method implements exponential backoff retry logic for connection
        failures and automatically recreates the database engine when needed.

        Yields:
            AsyncSession: Database session with connection recovery

        Raises:
            Exception: If all retry attempts are exhausted
        """
        if self.closed:
            raise RuntimeError("PostgresSessionManager has been closed")

        last_error = None

        for attempt in range(self.MAX_RETRIES):
            try:
                async_session_factory = sessionmaker(
                    bind=self.engine_async,
                    class_=AsyncSession,
                    expire_on_commit=False,
                    autoflush=False,
                    autocommit=False
                )

                async with async_session_factory() as session:
                    try:
                        yield session
                        # If we get here, the operation was successful
                        return
                    except Exception as err:
                        await session.rollback()

                        # Check if this is a connection error we can retry
                        if await self._handle_connection_error(err, f"session operation (attempt {attempt + 1})"):
                            last_error = err

                            # Don't retry on the last attempt
                            if attempt < self.MAX_RETRIES - 1:
                                self.logger.info(f"Retrying in {self.RETRY_DELAY * (attempt + 1)} seconds...")
                                await asyncio.sleep(self.RETRY_DELAY * (attempt + 1))

                                # Recreate the engine for the next attempt
                                await self._recreate_async_engine()
                                continue

                        # If it's not a connection error or we've exhausted retries, re-raise
                        raise

            except asyncio.CancelledError:
                self.logger.info("Async session was cancelled")
                raise
            except Exception as err:
                # Check if this is a connection error at the session creation level
                if await self._handle_connection_error(err, f"session creation (attempt {attempt + 1})"):
                    last_error = err

                    if attempt < self.MAX_RETRIES - 1:
                        self.logger.info(f"Retrying session creation in {self.RETRY_DELAY * (attempt + 1)} seconds...")
                        await asyncio.sleep(self.RETRY_DELAY * (attempt + 1))
                        await self._recreate_async_engine()
                        continue

                # If it's not a connection error or final attempt, re-raise
                raise

        # If we get here, all retries were exhausted
        self.logger.error(f"All {self.MAX_RETRIES} retry attempts exhausted")
        if last_error:
            raise last_error
        else:
            raise RuntimeError("Session creation failed after all retry attempts")

    @asynccontextmanager
    async def async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Enhanced async session context manager with connection recovery.

        This replaces the original async_session method with retry logic.
        """
        async with self.async_session_with_retry() as session:
            yield session

class DatabaseSessionManagerContainer(containers.DeclarativeContainer):
    """
    Dependency injection container for database session management.

    This container implements the Flowers refactoring pattern by:
    - Composing with LoggerContainer for centralized logging
    - Providing reusable database session managers
    - Managing database lifecycle through dependency injection
    - Enabling configuration override for different environments

    The container provides managed PostgreSQL session managers that automatically
    register with the lifecycle manager for proper cleanup. It supports both
    read-write and read-only database connections with configurable schemas.

    Attributes:
        logger_container: Composed LoggerContainer for logging services
        config: Configuration provider loaded from YAML file
        schema: Default database schema (defaults to "public")
        pg_rw_entry: Provider for read-write database entry details
        pg_ro_entry: Provider for read-only database entry details
        database_rw: Singleton managed session manager for read-write operations
        database_ro: Singleton managed session manager for read-only operations
        lifecycle_manager: Global database lifecycle manager for cleanup

    Example:
        >>> container = DatabaseSessionManagerContainer()
        >>> keepass_container = KeePassContainer()
        >>> container.pg_rw_entry.override(keepass_container.pg_rw)
        >>> container.pg_ro_entry.override(keepass_container.pg_ro)
        >>> db_rw = container.database_rw()
        >>> with db_rw.session() as session:
        ...     # Perform database operations
        ...     pass
    """

    # Container composition - include LoggerContainer
    logger_container = providers.Container(LoggerContainer)

    # Centralized configuration management
    config_path = os.getenv("LOGGING_CONFIG_PATH", "logging_config.yaml")
    config = providers.Configuration(yaml_files=[config_path])

    schema = providers.Object("public")

    # Improved wiring configuration
    wiring_config = containers.WiringConfiguration(
        modules=[
            "dags.data_pipeline.utility_code",
            "__main__",
            __name__,
        ]
    )

    # Entry providers - to be overridden by KeePass container
    pg_rw_entry = providers.Provider()
    pg_ro_entry = providers.Provider()

    # Use managed session managers with proper dependency injection
    database_rw = providers.Singleton(
        ManagedPostgresSessionManager,
        entry=pg_rw_entry.provided,
        schema=schema,
        rw=True,
        logger=logger_container.logger  # Inject logger from composed container
    )

    database_ro = providers.Singleton(
        ManagedPostgresSessionManager,
        entry=pg_ro_entry.provided,
        schema=schema,
        rw=False,
        logger=logger_container.logger  # Inject logger from composed container
    )

    # Lifecycle manager provider
    lifecycle_manager = providers.Object(db_lifecycle_manager)


# Context manager for proper async cleanup
@asynccontextmanager
async def database_lifecycle():
    """Context manager for managing database lifecycle in async applications."""
    try:
        yield
    finally:
        await db_lifecycle_manager.async_cleanup()


# Decorator for functions that need database cleanup
def with_database_cleanup(func):
    """Decorator that ensures database cleanup after function execution."""
    if asyncio.iscoroutinefunction(func):
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            finally:
                await db_lifecycle_manager.async_cleanup()
        return async_wrapper
    else:
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            finally:
                db_lifecycle_manager._sync_cleanup()
        return sync_wrapper



# Application shutdown handler
class ApplicationShutdownHandler:
    """Handles graceful shutdown of the application."""

    @inject
    def __init__(self, logger: Logger = Provide[LoggerContainer.logger]):
        self._shutdown_event = asyncio.Event()
        self._setup_signal_handlers()
        self.logger = logger

    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        import signal

        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, initiating shutdown...")
            asyncio.create_task(self.shutdown())

        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)

    async def shutdown(self):
        """Perform graceful shutdown."""
        if self._shutdown_event.is_set():
            return

        self._shutdown_event.set()
        self.logger.info("Starting graceful shutdown...")

        try:
            # Cleanup all PostgresSessionManager instances
            await PostgresSessionManager.cleanup_all_instances()
            self.logger.info("All database connections cleaned up successfully")
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")

        self.logger.info("Graceful shutdown completed")

    def is_shutting_down(self) -> bool:
        """Check if shutdown is in progress."""
        return self._shutdown_event.is_set()


# Global shutdown handler instance
shutdown_handler = ApplicationShutdownHandler()


def build_entry_details(keepass_manager: PyKeePass, title: str) -> EntryDetails:
    """Logic to retrieve and build EntryDetails, previously in get_kp_entry_details."""
    try:
        entry = keepass_manager.find_entries(title=title, first=True)
        if not entry:
            raise LookupError(f"No entry found with given title {title}")

        print(f"entry = {entry}")
        print("===========")

        builder = EntryDetailsBuilder()
        builder.set_username(entry.username).set_password(entry.password).set_url(entry.url)
        for custom_property in entry.custom_properties:
            builder.add_custom_property(custom_property, entry.get_custom_property(custom_property))

        return builder.build()

    except Exception as e:
        raise RuntimeError(f"Failed to find entry: {e}")


class FieldNameExtractor:
    def __init__(self, config):
        self.config = config

    def get_field_names(self):
        return [field_name["id"] for field_name in self.config["fields"]]

    def get_field_ids_by_datatype(self, datatype):
        """
        Given a datatype, returns the list of 'id' values for fields with the specified datatype.

        :param datatype: The datatype to filter by
        :return: List of 'id' values matching the datatype
        """
        return [field['id'] for field in self.config["fields"] if field['datatype'] == datatype]

# def instantiate_keepass() -> PyKeePass:
#     keepass_db = os.getenv("AIRFLOW_HOME") + "/Database.kdbx"
#     keepass_key = os.getenv("AIRFLOW_HOME") + "/Database.key"
#     if not keepass_db or not keepass_key:
#         raise ValueError("Environment variables DATABASE_PATH and MASTER_KEYFILE must be set.")
#
#     try:
#         ref = PyKeePass(keepass_db, keyfile=keepass_key)
#     except Exception as e:
#         raise ValueError(f"Failed to initialize Keepass reference: {e}")
#     return ref

class IssueFieldsContainer(containers.DeclarativeContainer):
    """
    Container for JIRA issue field configuration management.

    This container implements the Flowers refactoring pattern by:
    - Centralizing issue field configuration
    - Providing reusable field name extraction
    - Separating field configuration concerns
    - Enabling configuration override for different environments

    Attributes:
        config: Configuration provider for issue fields
        field_name_extractor: Factory for field name extraction utilities
    """

    # Specialized configuration for issue fields
    config_path = os.getenv("ISSUE_FIELDS_YAML_FILE", "issue_fields.yaml")
    config = providers.Configuration(yaml_files=[config_path])

    # Field name extractor with proper configuration injection
    field_name_extractor = providers.Factory(
        FieldNameExtractor,
        config=config,
    )

    # Improved wiring configuration
    wiring_config = containers.WiringConfiguration(
        modules=[
            "__main__",
            __name__,
            "dags.data_pipeline.utilities.profilers",
            "dags.data_pipeline.utility_code",
        ]
    )


class KeePassContainer(containers.DeclarativeContainer):
    """
    Container for KeePass credential management.

    This container implements the Flowers refactoring pattern by:
    - Centralizing credential management configuration
    - Providing reusable entry detail builders
    - Using consistent configuration management
    - Enabling environment-specific credential access

    Attributes:
        config: Configuration provider for KeePass settings
        keepass_manager: Singleton KeePass manager instance
        jira_entry_details: Factory for JIRA credential details
        pg_rw: Factory for PostgreSQL read-write credentials
        pg_ro: Factory for PostgreSQL read-only credentials
        schema_rw: Factory for schema-specific read-write credentials
        schema_ro: Factory for schema-specific read-only credentials
    """

    # Centralized configuration management
    config_path = os.getenv("LOGGING_CONFIG_PATH", "logging_config.yaml")
    config = providers.Configuration(yaml_files=[config_path])

    # Default titles for testing - can be overridden
    rw_title = providers.Object('test_rw')
    ro_title = providers.Object('test_ro')

    # Improved wiring configuration
    wiring_config = containers.WiringConfiguration(
        modules=[
            "dags.data_pipeline.containers",
            "dags.data_pipeline.utility_code",
            "__main__"
        ],
        from_package="dags.data_pipeline"
    )

    keepass_manager = providers.Singleton(
        PyKeePass,
        filename=config.KeePassDir.DB_NAME,
        keyfile=config.KeePassDir.KEY_FILE
    )

    # DON'T DELETE. KEEP IT FOR REFERENCE
    # keepass_manager = providers.Singleton(
    #     PyKeePass,
    #     filename=providers.Callable(lambda: os.getenv("AIRFLOW_HOME") + "/Database.kdbx"),
    #     keyfile=providers.Callable(lambda: os.getenv("AIRFLOW_HOME") + "/Database.key")
    # )
    # DON'T DELETE. KEEP IT FOR REFERENCE

    # Provider for `get_kp_entry_details`
    jira_entry_details = providers.Factory(
        build_entry_details,
        keepass_manager=keepass_manager,
        title=config.KeePass.JIRA_ENTRY,
    )

    pg_rw = providers.Factory(
        build_entry_details,
        keepass_manager=keepass_manager,
        title=config.KeePass.PG_RW,
    )

    pg_ro = providers.Factory(
        build_entry_details,
        keepass_manager=keepass_manager,
        title=config.KeePass.PG_RO,
    )

    schema_rw = providers.Factory(
        build_entry_details,
        keepass_manager=keepass_manager,
        title=rw_title,
    )

    schema_ro = providers.Factory(
        build_entry_details,
        keepass_manager=keepass_manager,
        title=ro_title,
    )


class JiraEntryDetailsContainer(containers.DeclarativeContainer):
    """
    Specialized container for JIRA credential management.

    This container implements the Flowers refactoring pattern by:
    - Separating JIRA-specific credential concerns
    - Providing reusable JIRA entry details
    - Using consistent configuration management
    - Enabling dependency injection for KeePass manager

    Attributes:
        config: Configuration provider for JIRA settings
        keepass: Provider for KeePass manager (to be injected)
        jira_entry_details: Singleton factory for JIRA credentials
    """

    # Centralized configuration management
    config_path = os.getenv("LOGGING_CONFIG_PATH", "logging_config.yaml")
    config = providers.Configuration(yaml_files=[config_path])

    # KeePass provider - to be overridden by parent container
    keepass = providers.Provider()

    # JIRA entry details with proper configuration
    jira_entry_details = providers.Singleton(
        build_entry_details,
        keepass_manager=keepass,
        title=config.KeePass.JIRA_ENTRY,
    )

    # Improved wiring configuration
    wiring_config = containers.WiringConfiguration([
        "__main__",
        "dags.data_pipeline.utility_code"
    ])



class GlobalCircuitBreaker:
    """
    Global circuit breaker that coordinates rate limiting across multiple async processes.
    Thread-safe and works on Windows/Unix.
    """

    @inject
    def __init__(
            self, config: CircuitBreakerConfig = None,
            logger_circuit: Logger = Provide[LoggerContainer.logger]

    ):
        self.config = config or CircuitBreakerConfig()
        self.logger = logger_circuit
        self._state = CircuitState.CLOSED
        self._failure_count = 0
        self._last_failure_time = 0
        self._half_open_calls = 0

        # Track rate limits separately from failures
        self._consecutive_rate_limits = 0

        # Asyncio events for coordination
        self._circuit_open_event = asyncio.Event()
        self._rate_limit_event = asyncio.Event()

        # Lock for thread safety
        self._lock = asyncio.Lock()

        # Rate limiting coordination
        self._active_requests = 0
        self._backoff_until = 0
        print(f"type self.logger = {type(self.logger)}")
        self.logger.debug("GlobalCircuitBreaker initialized and injection is working!!!.")


    async def can_execute(self) -> bool:
        """Check if request can be executed based on circuit state."""
        asyncio.current_task().set_name("can_execute")
        async with self._lock:
            current_time = time.time()

            if self._state == CircuitState.OPEN:
                if current_time - self._last_failure_time > self.config.recovery_timeout:
                    self._state = CircuitState.HALF_OPEN
                    self._half_open_calls = 0
                    self._circuit_open_event.clear()
                    self.logger.debug("can_execute: Circuit breaker transitioning to HALF_OPEN state.")
                    return True
                return False

            elif self._state == CircuitState.HALF_OPEN:
                if self._half_open_calls >= self.config.half_open_max_calls:
                    return False
                return True

            # CLOSED state - check for active rate limiting
            if current_time < self._backoff_until:
                self.logger.debug(f"can_execute: Rate limited. Waiting until {self._backoff_until}.")
                return False

            return True


    async def record_success(self):
        """Record successful request."""
        asyncio.current_task().set_name("record_success")
        async with self._lock:
            if self._state == CircuitState.HALF_OPEN:
                self._failure_count = 0
                self._consecutive_rate_limits = 0
                self._state = CircuitState.CLOSED
                self._circuit_open_event.clear()
                self.logger.debug(f"record_success: state= {self._state}")
            elif self._state == CircuitState.CLOSED:
                self._failure_count = max(0, self._failure_count - 1)
                self._consecutive_rate_limits = 0  # Reset on success
                self.logger.debug(f"record_success: _failure_count = {self._failure_count}")


    async def record_rate_limit(self, retry_delay: float):
        """Record rate limit hit and coordinate backoff."""
        asyncio.current_task().set_name("record_rate_limit")
        async with self._lock:
            current_time = time.time()
            self._backoff_until = current_time + (retry_delay / 1000)
            self._consecutive_rate_limits += 1

            # Only open circuit if we have excessive consecutive rate limits
            # This indicates the system is overwhelmed, not just rate limited
            max_consecutive_rate_limits = getattr(self.config, 'max_consecutive_rate_limits', 10)
            if self._consecutive_rate_limits >= max_consecutive_rate_limits:
                self._state = CircuitState.OPEN
                self._last_failure_time = current_time
                self._circuit_open_event.set()
                self.logger.warning(f"Circuit breaker OPEN due to {self._consecutive_rate_limits} consecutive rate limits")

            # Set rate limit event to signal other processes
            self._rate_limit_event.set()
            self.logger.debug(
                f"record_rate_limit: Rate limit hit. Backing off until {datetime.fromtimestamp(self._backoff_until, tz=ZoneInfo('Asia/Kolkata')).strftime('%Y-%m-%d %H:%M:%S %z')}.")


    async def record_failure(self):
        """Record actual service failures (not rate limits)."""
        asyncio.current_task().set_name("record_failure")
        async with self._lock:
            self._failure_count += 1
            self._consecutive_rate_limits = 0  # Reset since we got a response

            if self._failure_count >= self.config.failure_threshold:
                self._state = CircuitState.OPEN
                self._last_failure_time = time.time()
                self._circuit_open_event.set()
                self.logger.warning(f"Circuit breaker OPEN due to {self._failure_count} failures")


    async def wait_for_recovery(self, timeout: Optional[float] = None):
        """Wait for circuit breaker to recover or rate limit to clear."""
        asyncio.current_task().set_name("wait_for_recovery")
        if self._state == CircuitState.OPEN:
            try:
                await asyncio.wait_for(
                    self._circuit_open_event.wait(),
                    timeout=timeout
                )
                self.logger.debug("wait_for_recovery: Circuit breaker transitioned to CLOSED state.")
            except asyncio.TimeoutError:
                self.logger.debug("wait_for_recovery: Timeout waiting for circuit breaker recovery.")
                pass

        # Also wait for any active rate limiting to clear
        current_time = time.time()
        if current_time < self._backoff_until:
            wait_time = self._backoff_until - current_time
            if timeout is None or wait_time <= timeout:
                self.logger.debug(
                    f"wait_for_recovery: Waiting for rate limit to clear. Sleeping for {wait_time:.2f} seconds.")
                await asyncio.sleep(wait_time)
        else:
            self.logger.debug("wait_for_recovery: Rate limit cleared.")

    @inject
    async def enter_request(self, logger: Logger = Provide[LoggerContainer.logger]):
        """Enter a request context (for half-open state tracking)."""
        asyncio.current_task().set_name("enter_request")
        async with self._lock:
            self._active_requests += 1
            if self._state == CircuitState.HALF_OPEN:
                self._half_open_calls += 1
            logger.debug(f"enter_request: Entered request context. Active requests: {self._active_requests}")

    @inject
    async def exit_request(self, logger: Logger = Provide[LoggerContainer.logger]):
        """Exit a request context."""
        asyncio.current_task().set_name("exit_request")
        async with self._lock:
            self._active_requests = max(0, self._active_requests - 1)
            logger.debug(f"exit_request: Exited request context. Active requests: {self._active_requests}")

    @property
    def state(self) -> CircuitState:
        return self._state

    @property
    def is_open(self) -> bool:
        return self._state == CircuitState.OPEN

class QueueContainer(containers.DeclarativeContainer):
    # config_path = os.getenv("LOGGING_CONFIG_PATH", "./logging_config.yaml")
    # config_file = providers.Configuration(yaml_files=["./logging_config.yaml"])
    # config = providers.Configuration(yaml_files=[config_path])
    # Load queue names and max depths from the YAML file
    config = providers.Configuration()

    # queue_issues = providers.Singleton(asyncio.Queue, maxsize=1000)
    # queue_stats = providers.Singleton(asyncio.Queue, maxsize=500)
    # queue_issue = providers.Singleton(asyncio.Queue, maxsize=100)
    # queue_changelog = providers.Singleton(asyncio.Queue, maxsize=500)
    # queue_worklog = providers.Singleton(asyncio.Queue, maxsize=500)
    # queue_comment = providers.Singleton(asyncio.Queue, maxsize=500)
    # queue_issue_links = providers.Singleton(asyncio.Queue, maxsize=500)

    queues = providers.Dict(
        queue_issues=providers.Factory(asyncio.Queue, maxsize=1000),
        queue_stats=providers.Factory(asyncio.Queue, maxsize=500),
        queue_issue=providers.Factory(asyncio.Queue, maxsize=500),
        queue_changelog=providers.Factory(asyncio.Queue, maxsize=500),
        queue_worklog=providers.Factory(asyncio.Queue, maxsize=500),
        queue_comment=providers.Factory(asyncio.Queue, maxsize=500),
        queue_issue_links=providers.Factory(asyncio.Queue, maxsize=500),
        queue_upsert_issue=providers.Factory(asyncio.Queue),
    )

    # base_queues = providers.Singleton(queues)

    # # Map schema names to shared queue instances
    # queue_schema = providers.Singleton(
    #     providers.Dict({
    #         "acq": base_queues,
    #         "plat": base_queues,
    #         "plp": base_queues,
    #         "train": base_queues,
    #     })
    # )

    # # Active queues for the current schema
    # active_queues = providers.Factory(
    #     lambda schema_name: QueueContainer.queue_schema()[schema_name]
    # )
    database_rw = providers.Dependency(instance_of=PostgresSessionManager)
    schemas = providers.Callable(
        lambda db_manager: db_manager.get_schemas(),
        db_manager=database_rw
    )

    queue_selector = providers.Selector(
        config.schema_name,
        acq=providers.Singleton(queues),
        plp=providers.Singleton(queues),
        plat=providers.Singleton(queues),
        train=providers.Singleton(queues),
        cpp=providers.Singleton(queues),
    )
    wiring_config = containers.WiringConfiguration(["__main__", "utility_code"])


class ApplicationContainer(containers.DeclarativeContainer):
    """
    Main application container that provides all necessary dependencies.

    This container implements the Flowers refactoring pattern by:
    - Composing multiple specialized containers (Logger, KeePass, Database)
    - Providing centralized dependency management
    - Enabling configuration override and environment switching
    - Managing application lifecycle through dependency injection
    - Separating concerns across different container types

    This container serves as the central dependency injection hub for the application,
    providing access to:
    - Logging services through LoggerContainer composition
    - KeePass manager for credential management
    - Database session managers (both managed and unmanaged)
    - Configuration management
    - Lifecycle management for proper cleanup
    - Circuit breaker for resilience patterns

    The container supports dynamic schema switching and provides both Factory
    and Singleton providers for different use cases.

    Attributes:
        logger_container: Composed LoggerContainer for logging services
        config: Configuration loaded from YAML file
        schema: Current database schema (default: "public")
        keepass_manager: Singleton KeePass manager for credential access
        pg_rw_entry: Factory for read-write database entry details
        pg_ro_entry: Factory for read-only database entry details
        database_rw: Factory for unmanaged read-write session manager
        database_rw_managed: Singleton for managed read-write session manager
        database_ro: Factory for unmanaged read-only session manager
        database_ro_managed: Singleton for managed read-only session manager
        lifecycle_manager: Global database lifecycle manager
        circuit_breaker: Global circuit breaker for resilience

    Example:
        >>> container = ApplicationContainer()
        >>> container.wire(modules=[__name__])
        >>>
        >>> # Get managed database connection
        >>> db_rw = container.database_rw_managed()
        >>> with db_rw.session() as session:
        ...     # Database operations
        ...     pass
        >>>
        >>> # Schema switching
        >>> container.schema.override("plat")
        >>> db_plat = container.database_rw()
    """

    # Container composition - centralized logging
    logger_container = providers.Container(LoggerContainer)

    # Centralized configuration management
    config_path = os.getenv("LOGGING_CONFIG_PATH", "logging_config.yaml")
    config = providers.Configuration(yaml_files=[config_path])

    schema = providers.Object("public")  # Default schema
    # KeePass Manager
    keepass_manager = providers.Singleton(
        PyKeePass,
        filename=config.KeePassDir.DB_NAME,
        keyfile=config.KeePassDir.KEY_FILE
    )
    # Entry Providers
    pg_rw_entry = providers.Factory(
        build_entry_details,
        keepass_manager=keepass_manager,
        title=providers.Callable(lambda schema_name: f"{schema_name}_rw", schema_name=schema)
    )

    pg_ro_entry = providers.Factory(
        build_entry_details,
        keepass_manager=keepass_manager,
        title=providers.Callable(lambda schema_name: f"{schema_name}_ro", schema_name=schema)
    )

    # Database Session Managers with proper dependency injection
    database_rw = providers.Factory(
        PostgresSessionManager,
        entry=pg_rw_entry,
        schema=schema,
        rw=True,  # Fixed: should be True for read-write
        logger=logger_container.logger
    )

    database_rw_managed = providers.Singleton(
        ManagedPostgresSessionManager,
        entry=pg_rw_entry,
        schema=schema,
        rw=True,  # Fixed: should be True for read-write
        logger=logger_container.logger
    )

    database_rw_enhanced = providers.Factory(
        EnhancedPostgresSessionManager,
        entry=pg_rw_entry,
        schema=schema,
        rw=True,  # Fixed: should be True for read-write
        logger=logger_container.logger
    )

    database_ro = providers.Factory(
        PostgresSessionManager,
        entry=pg_ro_entry,
        schema=schema,
        rw=False,
        logger=logger_container.logger
    )

    database_ro_managed = providers.Singleton(
        ManagedPostgresSessionManager,  # Fixed: should be ManagedPostgresSessionManager
        entry=pg_ro_entry,
        schema=schema,
        rw=False,
        logger=logger_container.logger
    )

    database_ro_enhanced = providers.Factory(
        EnhancedPostgresSessionManager,
        entry=pg_ro_entry,  # Fixed: should be pg_ro_entry for read-only
        schema=schema,
        rw=False,  # Fixed: should be False for read-only
        logger=logger_container.logger
    )

    # Lifecycle manager
    lifecycle_manager = providers.Object(db_lifecycle_manager)

    # Circuit breaker with proper logger injection
    circuit_breaker = providers.Singleton(
        GlobalCircuitBreaker,
        logger_circuit=logger_container.logger,
    )

    # Wiring
    wiring_config = containers.WiringConfiguration(
        modules=["utility_code", "__main__", __name__]
    )


@inject
def get_kp_entry_details(
        title: str | None = None,
        ref: PyKeePass = Provide[KeePassContainer.keepass_manager]
) -> EntryDetails:
    try:
        entry = ref.find_entries(title=title, first=True)
        if not entry:
            raise LookupError(f"No entry found with given title")

        builder = EntryDetailsBuilder()
        builder.set_username(entry.username).set_password(entry.password).set_url(entry.url)
        for custom_property in entry.custom_properties:
            builder.add_custom_property(custom_property, entry.get_custom_property(custom_property))

        return builder.build()

    except Exception as e:
        raise RuntimeError(f"Failed to find entry: {e}")


# class KeePassDetailsContainer(containers.DeclarativeContainer):
#     # Separate container to avoid circular import issues
#     entry_details = providers.Factory(get_kp_entry_details, "corecard Jira")
#     wiring_config = containers.WiringConfiguration(["__main__"])
#
#     encoded_token = providers.Factory(
#         lambda entry: base64.b64encode(f"{entry.username}:{entry.password}".encode()).decode(),
#         entry=entry_details
#     )
#     headers = providers.Callable(
#         lambda token: {
#             'Accept': "application/json",
#             'Content-Type': "application/json",
#             'Authorization': f'Basic {token}'
#         },
#         token=providers.Factory(
#             lambda entry: base64.b64encode(f"{entry.username}:{entry.password}".encode()).decode(),
#             entry=entry_details
#         )
#     )


async def fetch_data(url: str):
    async with aiohttp.ClientSession().get(url) as response:
        print(response.status)
        return await response.json()  # or `await response.json()` if expecting JSON


async def send_data(url: str, data: dict):
    async with aiohttp.ClientSession().put(url, json=data) as response:
        return await response.json()


async def main():
    url = "https://example.com/api"
    data = {"key": "value"}

    # Use `http_session_provider` as an async context manager
    async with aiohttp.ClientSession() as http_session:
        # fetched_data = await fetch_data(url=url, http_session=http_session)
        # print(f"Fetched Data: {fetched_data}")

        # response_data = await send_data(url=url, data=data, http_session=http_session)
        # print(f"Response Data: {response_data}")
        response_fields = await fetch_data(url="https://corecard.atlassian.net/rest/api/3/field")
        print(json.dumps(response_fields))


def prepare_issue_classification_data(project_key: str, pg_session) -> pd.DataFrame:
    topq = pg_session.query(Issue.id, Issue.key, Issue.issuetype, Issue.isSubTask, Issue.parent_key,
                            literal(1).label('level'),
                            case(

                                (Issue.issuetype.in_(['Initiative ', 'Initiative']), 'initiative'),
                                (Issue.issuetype == 'Epic', 'epic'),
                                (Issue.isSubTask.is_(True), 'subtask'),

                                else_="standard"
                            ).label("issueclass"),
                            cast(cast(Issue.id, TEXT), LtreeType).label("path_id"),
                            cast(func.replace(Issue.key, "-", "_"), LtreeType).label("path_key")
                            )
    topq = topq.filter(Issue.parent_key.is_(None))
    topq = topq.cte('cte', recursive=True)

    bottomq = pg_session.query(Issue.id, Issue.key, Issue.issuetype, Issue.isSubTask, Issue.parent_key,
                               topq.c.level + 1,
                               case(
                                   (Issue.issuetype.in_(['Initiative ', 'Initiative']), 'initiative'),
                                   (Issue.issuetype == 'Epic', 'epic'),
                                   (Issue.isSubTask.is_(True), 'subtask'),
                                   else_="standard"
                               ).label("issueclass"),
                               topq.c.path_id.op('||')(func.text2ltree(cast(Issue.id, TEXT))),
                               topq.c.path_key.op('||')(func.text2ltree(func.replace(Issue.key, "-", "_")))
                               )
    bottomq = bottomq.join(topq, Issue.parent_key == topq.c.key)
    recursive_q = topq.union_all(bottomq)
    res = pg_session.query(recursive_q).all()
    df = pd.DataFrame(res)
    # df = pd.read_sql(session.query(recursive_q).statement, session.bind)
    return df


# Usage example with proper cleanup
async def main_with_cleanup():
    """Example of proper usage with cleanup."""
    try:
        # Your application logic here
        keepass_container = KeePassContainer()
        await keepass_container.init_resources()

        database_container = DatabaseSessionManagerContainer()
        database_container.pg_rw_entry.override(keepass_container.pg_rw)
        database_container.pg_ro_entry.override(keepass_container.pg_ro)
        await database_container.init_resources()

        # Use the database instances
        db_rw = database_container.database_rw()

        async with db_rw.async_session() as session:
            # Your database operations
            pass

    except asyncio.CancelledError:
        print("Main application was cancelled")
        raise
    except Exception as e:
        print(f"Error in main application: {e}")
        raise
    finally:
        # Ensure cleanup happens
        await shutdown_handler.shutdown()


# Example 1: Sync usage with automatic cleanup
@inject
def sync_database_operation(
        db_rw: ManagedPostgresSessionManager = Provide[ApplicationContainer.database_rw_managed]
):
    """Example sync function with database operations."""
    # with database_container.database_rw().update_schema('plat').session() as pg_session:
    print(f"sync_database_operation called")
    with db_rw.session() as session:
        print(f"database url plat: {session.bind.engine}")
        df = prepare_issue_classification_data('plat', session)
        print(f"Total rows: {df.shape[0]}")
    # Cleanup will happen automatically via atexit/signal handlers


# Example 2: Async usage with context manager
async def async_database_operation():
    """Example async function with proper cleanup."""
    container = ApplicationContainer()
    container.wire(modules=[__name__])

    async with database_lifecycle():
        db_rw = container.database_rw()
        async with db_rw.async_session() as session:
            print(session.bind.engine)

        # Cleanup happens automatically when exiting context


# Example 3: Manual cleanup control
@inject
async def manual_cleanup_example(
        lifecycle_manager: DatabaseLifecycleManager = Provide[ApplicationContainer.lifecycle_manager]
):
    """Example with manual cleanup control."""
    try:
        # Your application logic
        pass
    finally:
        # Manual cleanup when needed
        await lifecycle_manager.async_cleanup()


# Example 4: Decorator-based cleanup
@with_database_cleanup
async def decorated_function():
    """Function with automatic cleanup via decorator."""
    container = ApplicationContainer()
    await container.init_resources()

    db_rw = container.database_rw()
    async with db_rw.async_session() as session:
        # Your database operations
        pass
    # Cleanup happens automatically via decorator

if __name__ == "__main__":
    """
    Usage examples demonstrating different database operations across schemas.

    This section provides comprehensive examples for:
    - Working with different database schemas (public, plat, plp)
    - Both synchronous and asynchronous database operations
    - Proper cleanup and lifecycle management
    - Container configuration and dependency injection
    """

    def example_public_schema():
        """Example: Working with public schema."""
        print("\n=== PUBLIC SCHEMA EXAMPLE ===")

        # Initialize containers
        keepass_container = KeePassContainer()
        database_container = DatabaseSessionManagerContainer()

        # Configure database container with KeePass credentials
        database_container.pg_rw_entry.override(keepass_container.pg_rw)
        database_container.pg_ro_entry.override(keepass_container.pg_ro)
        database_container.schema.override('public')

        # Get managed database connection
        db_rw = database_container.database_rw()

        try:
            with db_rw.session() as session:
                print(f"Connected to public schema: {session.bind.engine}")
                # Example query
                stmt = select(User.displayName).limit(5)
                result = session.execute(stmt).all()
                print(f"Sample users: {result}")
        except Exception as e:
            print(f"Error in public schema example: {e}")

    def example_plat_schema():
        """Example: Working with plat schema."""
        print("\n=== PLAT SCHEMA EXAMPLE ===")

        # Initialize containers
        keepass_container = KeePassContainer()
        database_container = DatabaseSessionManagerContainer()

        # Configure for plat schema
        keepass_container.rw_title.override('plat_rw')
        keepass_container.ro_title.override('plat_ro')
        database_container.pg_rw_entry.override(keepass_container.schema_rw)
        database_container.pg_ro_entry.override(keepass_container.schema_ro)
        database_container.schema.override('plat')

        # Get database connection
        db_rw = database_container.database_rw()

        try:
            with db_rw.session() as session:
                print(f"Connected to plat schema: {session.bind.engine}")
                # Example: Get issue classification data
                df = prepare_issue_classification_data('plat', session)
                print(f"PLAT schema - Total issue rows: {df.shape[0]}")
        except Exception as e:
            print(f"Error in plat schema example: {e}")

    def example_plp_schema():
        """Example: Working with plp schema."""
        print("\n=== PLP SCHEMA EXAMPLE ===")

        # Initialize containers
        keepass_container = KeePassContainer()
        database_container = DatabaseSessionManagerContainer()

        # Configure for plp schema
        keepass_container.rw_title.override('plp_rw')
        keepass_container.ro_title.override('plp_ro')
        database_container.pg_rw_entry.override(keepass_container.schema_rw)
        database_container.pg_ro_entry.override(keepass_container.schema_ro)
        database_container.schema.override('plp')

        # Get database connection
        db_rw = database_container.database_rw()

        try:
            with db_rw.session() as session:
                print(f"Connected to plp schema: {session.bind.engine}")
                # Example: Get issue classification data
                df = prepare_issue_classification_data('plp', session)
                print(f"PLP schema - Total issue rows: {df.shape[0]}")
        except Exception as e:
            print(f"Error in plp schema example: {e}")

    async def example_async_operations():
        """Example: Asynchronous database operations with proper cleanup."""
        print("\n=== ASYNC OPERATIONS EXAMPLE ===")

        async with database_lifecycle():
            # Initialize containers
            keepass_container = KeePassContainer()
            database_container = DatabaseSessionManagerContainer()

            # Configure database container
            database_container.pg_rw_entry.override(keepass_container.pg_rw)
            database_container.pg_ro_entry.override(keepass_container.pg_ro)

            # Get managed database connection
            db_rw = database_container.database_rw()

            try:
                async with db_rw.async_session() as session:
                    print(f"Async connection: {session.bind.engine}")
                    # Example async query
                    stmt = select(User.displayName).limit(3)
                    result = await session.execute(stmt)
                    users = result.all()
                    print(f"Async query result: {users}")

                # Test schema switching
                db_rw.update_schema('plat')
                async with db_rw.async_session() as session:
                    print(f"Switched to plat schema: {session.bind.engine}")

            except Exception as e:
                print(f"Error in async operations: {e}")

    def example_application_container():
        """Example: Using ApplicationContainer for dependency injection."""
        print("\n=== APPLICATION CONTAINER EXAMPLE ===")

        # Initialize and wire the application container
        container = ApplicationContainer()
        container.wire(modules=[__name__])

        try:
            # Use injected dependencies
            sync_database_operation()

            # Manual container usage
            db_managed = container.database_rw_managed()
            with db_managed.session() as session:
                print(f"Managed connection: {session.bind.engine}")

        except Exception as e:
            print(f"Error in application container example: {e}")

    def example_jira_credentials():
        """Example: Accessing Jira credentials from KeePass."""
        print("\n=== JIRA CREDENTIALS EXAMPLE ===")

        try:
            # Get Jira credentials
            cc_jira = get_kp_entry_details("corecard Jira")
            auth_token = f'{cc_jira.username}:{cc_jira.password}'
            encoded_token = base64.b64encode(auth_token.encode()).decode()
            print(f"Jira auth token (base64): {encoded_token[:20]}...")

        except Exception as e:
            print(f"Error accessing Jira credentials: {e}")

    # Run all examples
    print("Running database container usage examples...")

    try:
        # Synchronous examples
        example_public_schema()
        example_plat_schema()
        example_plp_schema()
        example_application_container()
        example_jira_credentials()

        # Asynchronous example
        print("\nRunning async example...")
        asyncio.run(example_async_operations())

    except KeyboardInterrupt:
        print("\nExamples interrupted by user")
    except Exception as e:
        print(f"\nError running examples: {e}")

    print("\nAll examples completed. Database connections will be cleaned up automatically.")
